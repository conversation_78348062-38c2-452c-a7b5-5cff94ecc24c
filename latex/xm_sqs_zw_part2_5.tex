%!TEX program = xelatex

% 编译顺序: xelatex -> bibtex -> xelatex -> xelatex
% 国家自然科学基金NSFC面上项目申请书正文模板（2023年版）version1.0
% 声明：
% 注意！！！非国家自然科学基金委官方模版！！！由个人根据官方MsWord模版制作。本模版的作者尽力使本模版和官方模版生成的PDF文件视觉效果大致一样，然而，并不保证本模版有用，也不对使用本模版造成的任何直接或间接后果负责。 不得将本模版用于商用或获取经济利益。本模版可以自由修改以满足用户自己的需要。但是如果要传播本模版，则只能传播未经修改的版本。使用本模版意味着同意上述声明。
% 强烈建议自己对照官方MsWord模板确认格式和文字是否一致，尤其是蓝字部分。
% 如有问题，可以发邮件询问原作者 <EMAIL>，或本版本的修改者 <EMAIL>


\documentclass[12pt,UTF8,AutoFakeBold=3,a4paper]{ctexart} %默认小四号字。允许楷体粗体。
\usepackage[english]{babel} %支持混合语言
\usepackage[dvipsnames]{xcolor}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}

\usepackage{xeCJKfntef}

\usepackage[backend=biber,style=ieee]{biblatex}
\addbibresource{myexample.bib}

% ==================================================
% a command to bold author's name
% ==================================================
\DeclareSourcemap{
  \maps[datatype=bibtex]{
    \map{
      \step[fieldsource=author,
            match=\regexp{Pan,\s*Yiwen},
            replace=\regexp{\\underline{\\textbf{Y. \~ Pan}}}
           ]
    }
  }
}
% ==================================================

% 设置 biblatex 字体大小
\renewcommand*{\bibfont}{\xiaowuhao}

\usepackage{pifont}
\usepackage{graphicx}
\usepackage{ulem}
% \usepackage{CJKulem}
\usepackage{setspace}
\usepackage{enumitem}
\usepackage{amsmath} %更多数学符号
\usepackage{mathtools}
\usepackage[unicode]{hyperref} %提供跳转链接
\usepackage{geometry} %改改尺寸
\usepackage{amsfonts}

\usepackage{tikz}
\usepackage{bibentry}
\nobibliography*
% \usepackage[numbers,sort&compress]{natbib}
% \setlength{\bibsep}{0.0ex} % 缩小参考文献间的垂直间距


%latex的页边距比word的视觉效果要大一些，稍微调整一下
\geometry{left=2.545cm,right=2.545cm,top=2.67cm,bottom=3.27cm}
\pagestyle{empty}
\setcounter{secnumdepth}{-2} %不让那些section和subsection自带标号，标号格式自己掌握
\definecolor{MsBlue}{RGB}{0,112,192} %Ms Word 的蓝色和latex xcolor包预定义的蓝色不一样。通过屏幕取色得到。
\definecolor{BoldBlue}{RGB}{0,0,64} % Color(0, 0, 64)
% Renaming floats with babel
\addto\captionsenglish{
    \renewcommand{\contentsname}{目录}
    \renewcommand{\listfigurename}{插图目录}
    \renewcommand{\listtablename}{表格}
    % \renewcommand{\refname}{\sihao \kaishu 参考文献}
    \renewcommand{\refname}{\sihao \kaishu \leftline{参考文献}} %这几个字默认字号稍大，改成四号字，楷书，居左(默认居中) 根据喜好自行修改，官方模板未作要求
    \renewcommand{\abstractname}{摘要}
    \renewcommand{\indexname}{索引}
    \renewcommand{\tablename}{表}
    \renewcommand{\figurename}{\kaishu 图}
    } %把Figure改成'图'，reference改成'参考文献'。如此处理是为了避免和babel包冲突。
%定义字号
\newcommand{\chuhao}{\fontsize{42pt}{\baselineskip}\selectfont}
\newcommand{\xiaochuhao}{\fontsize{36pt}{\baselineskip}\selectfont}
\newcommand{\yihao}{\fontsize{26pt}{\baselineskip}\selectfont}
\newcommand{\erhao}{\fontsize{22pt}{\baselineskip}\selectfont}
\newcommand{\xiaoerhao}{\fontsize{18pt}{\baselineskip}\selectfont}
\newcommand{\sanhao}{\fontsize{16pt}{\baselineskip}\selectfont}
\newcommand{\sihao}{\fontsize{14pt}{\baselineskip}\selectfont}
\newcommand{\xiaosihao}{\fontsize{12pt}{\baselineskip}\selectfont}
\newcommand{\wuhao}{\fontsize{10.5pt}{\baselineskip}\selectfont}
\newcommand{\xiaowuhao}{\fontsize{9pt}{\baselineskip}\selectfont}
\newcommand{\liuhao}{\fontsize{7.875pt}{\baselineskip}\selectfont}
\newcommand{\qihao}{\fontsize{5.25pt}{\baselineskip}\selectfont}
%字号对照表
%二号 21pt
%四号 14
%小四 12
%五号 10.5
%设置行距 1.5倍
\renewcommand{\baselinestretch}{1.5}
\XeTeXlinebreaklocale "zh"           % 中文断行
% \usepackage{tikz}
% \newcommand*\circled[1]{\tikz[baseline=(char.base)]{
%             \node[shape=circle,draw,inner sep=2pt] (char) {#1};}}
%%%% 正文开始 %%%%
\begin{document}







{\sihao \noindent \bfseries{\heiti{（一）申报项目与所属指南方向的关联关系}}}

\vspace{0.6em}
{\xiaosihao \noindent 1、针对项目研究拟解决的问题，拟采用的方法、原理、机理、算法、模型等
}

{\xiaosihao \noindent 限2000字以内。}



\vspace{5em}


{\xiaosihao \noindent 2、项目研究方法（技术路线）的可行性、先进性分析
}

{\xiaosihao \noindent 限2000字以内。}



\end{document}


